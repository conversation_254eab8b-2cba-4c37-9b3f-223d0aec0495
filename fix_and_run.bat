@echo off
chcp 65001 >nul
echo 🔧 إصلاح وتشغيل نظام إدارة المخزون
echo ==================================================

echo 🐘 تشغيل قاعدة البيانات...
docker-compose up -d

if %errorlevel% neq 0 (
    echo ❌ فشل في تشغيل قاعدة البيانات
    echo 📥 تأكد من تثبيت Docker Desktop
    pause
    exit /b 1
)

echo ✅ تم تشغيل قاعدة البيانات بنجاح
echo ⏳ انتظار تشغيل قاعدة البيانات...
timeout /t 5 /nobreak >nul

echo 🔧 إصلاح مشاكل النظام...
cd backend
python fix_login.py

if %errorlevel% neq 0 (
    echo ❌ فشل في إصلاح المشاكل
    pause
    exit /b 1
)

echo 🚀 تشغيل الخادم...
start "Backend Server" cmd /k "python test_server.py"
cd ..

echo ⏳ انتظار تشغيل Backend...
timeout /t 10 /nobreak >nul

echo ⚛️ تشغيل Frontend...
cd frontend

if not exist node_modules (
    echo 📦 تثبيت مكتبات Frontend...
    npm install
)

start "Frontend Server" cmd /k "npm start"
cd ..

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo 🌐 الواجهة الأمامية: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 📚 توثيق API: http://localhost:8000/docs
echo.
echo 👤 بيانات الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 🛑 لإيقاف النظام أغلق نوافذ الخوادم
echo.
pause
