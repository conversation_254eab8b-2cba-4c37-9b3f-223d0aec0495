{"ast": null, "code": "// This module is based on https://github.com/airbnb/prop-types-exact repository.\n// However, in order to reduce the number of dependencies and to remove some extra safe checks\n// the module was forked.\n\nconst specialProperty = 'exact-prop: \\u200b';\nexport default function exactProp(propTypes) {\n  if (process.env.NODE_ENV === 'production') {\n    return propTypes;\n  }\n  return {\n    ...propTypes,\n    [specialProperty]: props => {\n      const unsupportedProps = Object.keys(props).filter(prop => !propTypes.hasOwnProperty(prop));\n      if (unsupportedProps.length > 0) {\n        return new Error(`The following props are not supported: ${unsupportedProps.map(prop => `\\`${prop}\\``).join(', ')}. Please remove them.`);\n      }\n      return null;\n    }\n  };\n}", "map": {"version": 3, "names": ["specialProperty", "exactProp", "propTypes", "process", "env", "NODE_ENV", "props", "unsupportedProps", "Object", "keys", "filter", "prop", "hasOwnProperty", "length", "Error", "map", "join"], "sources": ["E:/a1/frontend/node_modules/@mui/utils/esm/exactProp/exactProp.js"], "sourcesContent": ["// This module is based on https://github.com/airbnb/prop-types-exact repository.\n// However, in order to reduce the number of dependencies and to remove some extra safe checks\n// the module was forked.\n\nconst specialProperty = 'exact-prop: \\u200b';\nexport default function exactProp(propTypes) {\n  if (process.env.NODE_ENV === 'production') {\n    return propTypes;\n  }\n  return {\n    ...propTypes,\n    [specialProperty]: props => {\n      const unsupportedProps = Object.keys(props).filter(prop => !propTypes.hasOwnProperty(prop));\n      if (unsupportedProps.length > 0) {\n        return new Error(`The following props are not supported: ${unsupportedProps.map(prop => `\\`${prop}\\``).join(', ')}. Please remove them.`);\n      }\n      return null;\n    }\n  };\n}"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAMA,eAAe,GAAG,oBAAoB;AAC5C,eAAe,SAASC,SAASA,CAACC,SAAS,EAAE;EAC3C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAOH,SAAS;EAClB;EACA,OAAO;IACL,GAAGA,SAAS;IACZ,CAACF,eAAe,GAAGM,KAAK,IAAI;MAC1B,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,MAAM,CAACC,IAAI,IAAI,CAACT,SAAS,CAACU,cAAc,CAACD,IAAI,CAAC,CAAC;MAC3F,IAAIJ,gBAAgB,CAACM,MAAM,GAAG,CAAC,EAAE;QAC/B,OAAO,IAAIC,KAAK,CAAC,0CAA0CP,gBAAgB,CAACQ,GAAG,CAACJ,IAAI,IAAI,KAAKA,IAAI,IAAI,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC;MAC3I;MACA,OAAO,IAAI;IACb;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}