#!/usr/bin/env python3
"""
إصلاح مشاكل تسجيل الدخول
"""
import asyncio
import subprocess
import sys

def install_requirements():
    """تثبيت المتطلبات"""
    try:
        print("📦 تثبيت المتطلبات...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

async def fix_database():
    """إصلاح قاعدة البيانات"""
    try:
        from tortoise import Tortoise
        
        DATABASE_URL = "postgres://inventory_user:inventory_pass@localhost:5432/inventory_db"
        
        print("🔧 إصلاح قاعدة البيانات...")
        
        # الاتصال بقاعدة البيانات
        await Tortoise.init(
            db_url=DATABASE_URL,
            modules={"models": ["app.models"]}
        )
        
        # إنشاء الجداول
        await Tortoise.generate_schemas()
        print("✅ تم إنشاء/تحديث الجداول")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        print("💡 تأكد من تشغيل PostgreSQL: docker-compose up -d")
        return False

async def create_admin_user():
    """إنشاء مستخدم مدير"""
    try:
        from tortoise import Tortoise
        from app.models.user import User, UserRole
        from app.auth.security import get_password_hash
        
        DATABASE_URL = "postgres://inventory_user:inventory_pass@localhost:5432/inventory_db"
        
        await Tortoise.init(
            db_url=DATABASE_URL,
            modules={"models": ["app.models"]}
        )
        
        print("👤 إنشاء مستخدم مدير...")
        
        # حذف المستخدم القديم إذا كان موجوداً
        await User.filter(username="admin").delete()
        
        # إنشاء مستخدم جديد
        admin_user = await User.create(
            username="admin",
            email="<EMAIL>",
            full_name="مدير النظام",
            hashed_password=get_password_hash("admin123"),
            role=UserRole.ADMIN,
            is_active=True
        )
        
        print(f"✅ تم إنشاء المستخدم: {admin_user.username}")
        print(f"📧 البريد الإلكتروني: {admin_user.email}")
        print(f"🔑 كلمة المرور: admin123")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return False

async def test_login():
    """اختبار تسجيل الدخول"""
    try:
        from tortoise import Tortoise
        from app.models.user import User
        from app.auth.security import verify_password, create_access_token
        
        DATABASE_URL = "postgres://inventory_user:inventory_pass@localhost:5432/inventory_db"
        
        await Tortoise.init(
            db_url=DATABASE_URL,
            modules={"models": ["app.models"]}
        )
        
        print("🔍 اختبار تسجيل الدخول...")
        
        # البحث عن المستخدم
        user = await User.get_or_none(username="admin")
        
        if not user:
            print("❌ المستخدم غير موجود")
            return False
        
        # اختبار كلمة المرور
        if not verify_password("admin123", user.hashed_password):
            print("❌ كلمة المرور غير صحيحة")
            return False
        
        # إنشاء توكن
        token = create_access_token(data={"sub": user.username})
        
        print("✅ تسجيل الدخول نجح")
        print(f"🎫 التوكن: {token[:50]}...")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الدخول: {e}")
        return False

def update_requirements():
    """تحديث ملف المتطلبات"""
    requirements = """fastapi==0.104.1
uvicorn[standard]==0.24.0
tortoise-orm[asyncpg]==0.20.0
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
passlib[bcrypt]==1.7.4
python-decouple==3.8
pydantic==2.5.0
pydantic-settings==2.1.0
asyncpg==0.29.0
aerich==0.7.2
pillow==10.1.0
aiofiles==23.2.1"""
    
    try:
        with open("requirements.txt", "w", encoding="utf-8") as f:
            f.write(requirements)
        print("✅ تم تحديث ملف المتطلبات")
        return True
    except Exception as e:
        print(f"❌ خطأ في تحديث المتطلبات: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🔧 بدء إصلاح مشاكل تسجيل الدخول...")
    print("=" * 50)
    
    # تحديث المتطلبات
    if not update_requirements():
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        return
    
    # إصلاح قاعدة البيانات
    if not await fix_database():
        return
    
    # إنشاء مستخدم مدير
    if not await create_admin_user():
        return
    
    # اختبار تسجيل الدخول
    if not await test_login():
        return
    
    print("\n" + "=" * 50)
    print("✅ تم إصلاح جميع المشاكل!")
    print("\n🚀 يمكنك الآن تشغيل النظام:")
    print("   python test_server.py")
    print("\n🌐 ثم انتقل إلى: http://localhost:3000")
    print("👤 بيانات الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")

if __name__ == "__main__":
    asyncio.run(main())
