[{"E:\\a1\\frontend\\src\\index.tsx": "1", "E:\\a1\\frontend\\src\\reportWebVitals.ts": "2", "E:\\a1\\frontend\\src\\App.tsx": "3", "E:\\a1\\frontend\\src\\services\\api.ts": "4", "E:\\a1\\frontend\\src\\components\\Auth\\Login.tsx": "5", "E:\\a1\\frontend\\src\\components\\Products\\ProductList.tsx": "6", "E:\\a1\\frontend\\src\\components\\Layout\\Sidebar.tsx": "7", "E:\\a1\\frontend\\src\\components\\Suppliers\\SupplierList.tsx": "8", "E:\\a1\\frontend\\src\\components\\Dashboard\\Dashboard.tsx": "9", "E:\\a1\\frontend\\src\\components\\Categories\\CategoryList.tsx": "10", "E:\\a1\\frontend\\src\\components\\Inventory\\InventoryMovements.tsx": "11"}, {"size": 554, "mtime": 1749376660600, "results": "12", "hashOfConfig": "13"}, {"size": 425, "mtime": 1749376660599, "results": "14", "hashOfConfig": "13"}, {"size": 3474, "mtime": 1749377405143, "results": "15", "hashOfConfig": "13"}, {"size": 5005, "mtime": 1749380037811, "results": "16", "hashOfConfig": "13"}, {"size": 3214, "mtime": 1749376794092, "results": "17", "hashOfConfig": "13"}, {"size": 10062, "mtime": 1749376869559, "results": "18", "hashOfConfig": "13"}, {"size": 3211, "mtime": 1749376810255, "results": "19", "hashOfConfig": "13"}, {"size": 12355, "mtime": 1749377338979, "results": "20", "hashOfConfig": "13"}, {"size": 8195, "mtime": 1749376837983, "results": "21", "hashOfConfig": "13"}, {"size": 8509, "mtime": 1749377121406, "results": "22", "hashOfConfig": "13"}, {"size": 12286, "mtime": 1749377377289, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13pyw8d", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\a1\\frontend\\src\\index.tsx", [], [], "E:\\a1\\frontend\\src\\reportWebVitals.ts", [], [], "E:\\a1\\frontend\\src\\App.tsx", ["57"], [], "E:\\a1\\frontend\\src\\services\\api.ts", [], [], "E:\\a1\\frontend\\src\\components\\Auth\\Login.tsx", [], [], "E:\\a1\\frontend\\src\\components\\Products\\ProductList.tsx", ["58", "59"], [], "E:\\a1\\frontend\\src\\components\\Layout\\Sidebar.tsx", [], [], "E:\\a1\\frontend\\src\\components\\Suppliers\\SupplierList.tsx", ["60"], [], "E:\\a1\\frontend\\src\\components\\Dashboard\\Dashboard.tsx", [], [], "E:\\a1\\frontend\\src\\components\\Categories\\CategoryList.tsx", ["61", "62", "63"], [], "E:\\a1\\frontend\\src\\components\\Inventory\\InventoryMovements.tsx", ["64", "65", "66", "67"], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 49, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 49, "endColumn": 14}, {"ruleId": "72", "severity": 1, "message": "73", "line": 75, "column": 6, "nodeType": "74", "endLine": 75, "endColumn": 8, "suggestions": "75"}, {"ruleId": "72", "severity": 1, "message": "76", "line": 79, "column": 6, "nodeType": "74", "endLine": 79, "endColumn": 68, "suggestions": "77"}, {"ruleId": "72", "severity": 1, "message": "78", "line": 67, "column": 6, "nodeType": "74", "endLine": 67, "endColumn": 18, "suggestions": "79"}, {"ruleId": "68", "severity": 1, "message": "80", "line": 8, "column": 3, "nodeType": "70", "messageId": "71", "endLine": 8, "endColumn": 14}, {"ruleId": "68", "severity": 1, "message": "81", "line": 23, "column": 3, "nodeType": "70", "messageId": "71", "endLine": 23, "endColumn": 7}, {"ruleId": "68", "severity": 1, "message": "82", "line": 24, "column": 3, "nodeType": "70", "messageId": "71", "endLine": 24, "endColumn": 9}, {"ruleId": "68", "severity": 1, "message": "83", "line": 72, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 72, "endColumn": 22}, {"ruleId": "68", "severity": 1, "message": "84", "line": 72, "column": 24, "nodeType": "70", "messageId": "71", "endLine": 72, "endColumn": 39}, {"ruleId": "68", "severity": 1, "message": "85", "line": 73, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 73, "endColumn": 25}, {"ruleId": "68", "severity": 1, "message": "86", "line": 73, "column": 27, "nodeType": "70", "messageId": "71", "endLine": 73, "endColumn": 45}, "@typescript-eslint/no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["87"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["88"], "React Hook useEffect has a missing dependency: 'fetchSuppliers'. Either include it or remove the dependency array.", ["89"], "'CardActions' is defined but never used.", "'Edit' is defined but never used.", "'Delete' is defined but never used.", "'movementType' is assigned a value but never used.", "'setMovementType' is assigned a value but never used.", "'selectedProduct' is assigned a value but never used.", "'setSelectedProduct' is assigned a value but never used.", {"desc": "90", "fix": "91"}, {"desc": "92", "fix": "93"}, {"desc": "94", "fix": "95"}, "Update the dependencies array to be: [fetchData]", {"range": "96", "text": "97"}, "Update the dependencies array to be: [fetchProducts, searchTerm, selectedCategory, selectedSupplier, showLowStock]", {"range": "98", "text": "99"}, "Update the dependencies array to be: [fetchSuppliers, searchTerm]", {"range": "100", "text": "101"}, [1674, 1676], "[fetchData]", [1726, 1788], "[fetchProducts, searchTerm, selectedCategory, selectedSupplier, showLowStock]", [1460, 1472], "[fetchSuppliers, searchTerm]"]