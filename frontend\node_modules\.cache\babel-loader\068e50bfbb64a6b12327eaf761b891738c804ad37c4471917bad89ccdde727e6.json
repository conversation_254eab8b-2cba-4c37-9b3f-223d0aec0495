{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:8000/api';\n\n// إنشاء instance من axios\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// إضافة interceptor للتوكن\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('access_token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// إضافة interceptor للاستجابة\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // إزالة التوكن وإعادة توجيه للتسجيل\n    localStorage.removeItem('access_token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// خدمات المصادقة\nexport const authAPI = {\n  login: (username, password) => {\n    // استخدام JSON بدلاً من FormData للخادم المبسط\n    return api.post('/auth/login', {\n      username,\n      password\n    });\n  },\n  register: userData => api.post('/auth/register', userData),\n  getCurrentUser: () => api.get('/auth/me')\n};\n\n// خدمات المستخدمين\nexport const usersAPI = {\n  getUsers: (skip = 0, limit = 100) => api.get(`/users?skip=${skip}&limit=${limit}`),\n  getUser: id => api.get(`/users/${id}`),\n  createUser: userData => api.post('/users', userData),\n  updateUser: (id, userData) => api.put(`/users/${id}`, userData),\n  deleteUser: id => api.delete(`/users/${id}`)\n};\n\n// خدمات الفئات\nexport const categoriesAPI = {\n  getCategories: (skip = 0, limit = 100) => api.get(`/categories?skip=${skip}&limit=${limit}`),\n  getCategoriesTree: () => api.get('/categories/tree'),\n  getCategory: id => api.get(`/categories/${id}`),\n  createCategory: categoryData => api.post('/categories', categoryData),\n  updateCategory: (id, categoryData) => api.put(`/categories/${id}`, categoryData),\n  deleteCategory: id => api.delete(`/categories/${id}`)\n};\n\n// خدمات الموردين\nexport const suppliersAPI = {\n  getSuppliers: (skip = 0, limit = 100, search) => {\n    let url = `/suppliers?skip=${skip}&limit=${limit}`;\n    if (search) url += `&search=${encodeURIComponent(search)}`;\n    return api.get(url);\n  },\n  getSupplier: id => api.get(`/suppliers/${id}`),\n  createSupplier: supplierData => api.post('/suppliers', supplierData),\n  updateSupplier: (id, supplierData) => api.put(`/suppliers/${id}`, supplierData),\n  deleteSupplier: id => api.delete(`/suppliers/${id}`)\n};\n\n// خدمات المنتجات\nexport const productsAPI = {\n  getProducts: (params = {}) => {\n    const queryParams = new URLSearchParams();\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null) {\n        queryParams.append(key, params[key]);\n      }\n    });\n    return api.get(`/products?${queryParams.toString()}`);\n  },\n  getLowStockProducts: () => api.get('/products/low-stock'),\n  getProduct: id => api.get(`/products/${id}`),\n  createProduct: productData => api.post('/products', productData),\n  updateProduct: (id, productData) => api.put(`/products/${id}`, productData),\n  deleteProduct: id => api.delete(`/products/${id}`)\n};\n\n// خدمات المخزون\nexport const inventoryAPI = {\n  getMovements: (params = {}) => {\n    const queryParams = new URLSearchParams();\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null) {\n        queryParams.append(key, params[key]);\n      }\n    });\n    return api.get(`/inventory/movements?${queryParams.toString()}`);\n  },\n  createMovement: movementData => api.post('/inventory/movements', movementData),\n  adjustStock: adjustmentData => api.post('/inventory/adjust-stock', adjustmentData),\n  getStockLevels: (lowStockOnly = false) => api.get(`/inventory/stock-levels?low_stock_only=${lowStockOnly}`)\n};\n\n// خدمات التقارير\nexport const reportsAPI = {\n  getDashboardStats: () => api.get('/reports/dashboard'),\n  getInventorySummary: categoryId => {\n    let url = '/reports/inventory-summary';\n    if (categoryId) url += `?category_id=${categoryId}`;\n    return api.get(url);\n  },\n  getMovementAnalysis: (startDate, endDate) => {\n    let url = '/reports/movement-analysis';\n    const params = new URLSearchParams();\n    if (startDate) params.append('start_date', startDate);\n    if (endDate) params.append('end_date', endDate);\n    if (params.toString()) url += `?${params.toString()}`;\n    return api.get(url);\n  },\n  getLowStockAlert: () => api.get('/reports/low-stock-alert'),\n  getSupplierAnalysis: () => api.get('/reports/supplier-analysis')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "authAPI", "login", "username", "password", "post", "register", "userData", "getCurrentUser", "get", "usersAPI", "getUsers", "skip", "limit", "getUser", "id", "createUser", "updateUser", "put", "deleteUser", "delete", "categoriesAPI", "getCategories", "getCategoriesTree", "getCategory", "createCategory", "categoryData", "updateCategory", "deleteCategory", "suppliersAPI", "getSuppliers", "search", "url", "encodeURIComponent", "getSupplier", "createSupplier", "supplierData", "updateSupplier", "deleteSupplier", "productsAPI", "getProducts", "params", "queryParams", "URLSearchParams", "Object", "keys", "for<PERSON>ach", "key", "undefined", "append", "toString", "getLowStockProducts", "getProduct", "createProduct", "productData", "updateProduct", "deleteProduct", "inventoryAPI", "getMovements", "createMovement", "movementData", "adjustStock", "adjustmentData", "getStockLevels", "lowStockOnly", "reportsAPI", "getDashboardStats", "getInventorySummary", "categoryId", "getMovementAnalysis", "startDate", "endDate", "getLowStockAlert", "getSupplierAnalysis"], "sources": ["E:/a1/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\n// إنشاء instance من axios\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// إضافة interceptor للتوكن\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('access_token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// إضافة interceptor للاستجابة\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // إزالة التوكن وإعادة توجيه للتسجيل\n      localStorage.removeItem('access_token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// خدمات المصادقة\nexport const authAPI = {\n  login: (username: string, password: string) => {\n    // استخدام JSON بدلاً من FormData للخادم المبسط\n    return api.post('/auth/login', {\n      username,\n      password\n    });\n  },\n  register: (userData: any) => api.post('/auth/register', userData),\n  getCurrentUser: () => api.get('/auth/me'),\n};\n\n// خدمات المستخدمين\nexport const usersAPI = {\n  getUsers: (skip = 0, limit = 100) => api.get(`/users?skip=${skip}&limit=${limit}`),\n  getUser: (id: number) => api.get(`/users/${id}`),\n  createUser: (userData: any) => api.post('/users', userData),\n  updateUser: (id: number, userData: any) => api.put(`/users/${id}`, userData),\n  deleteUser: (id: number) => api.delete(`/users/${id}`),\n};\n\n// خدمات الفئات\nexport const categoriesAPI = {\n  getCategories: (skip = 0, limit = 100) => api.get(`/categories?skip=${skip}&limit=${limit}`),\n  getCategoriesTree: () => api.get('/categories/tree'),\n  getCategory: (id: number) => api.get(`/categories/${id}`),\n  createCategory: (categoryData: any) => api.post('/categories', categoryData),\n  updateCategory: (id: number, categoryData: any) => api.put(`/categories/${id}`, categoryData),\n  deleteCategory: (id: number) => api.delete(`/categories/${id}`),\n};\n\n// خدمات الموردين\nexport const suppliersAPI = {\n  getSuppliers: (skip = 0, limit = 100, search?: string) => {\n    let url = `/suppliers?skip=${skip}&limit=${limit}`;\n    if (search) url += `&search=${encodeURIComponent(search)}`;\n    return api.get(url);\n  },\n  getSupplier: (id: number) => api.get(`/suppliers/${id}`),\n  createSupplier: (supplierData: any) => api.post('/suppliers', supplierData),\n  updateSupplier: (id: number, supplierData: any) => api.put(`/suppliers/${id}`, supplierData),\n  deleteSupplier: (id: number) => api.delete(`/suppliers/${id}`),\n};\n\n// خدمات المنتجات\nexport const productsAPI = {\n  getProducts: (params: any = {}) => {\n    const queryParams = new URLSearchParams();\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null) {\n        queryParams.append(key, params[key]);\n      }\n    });\n    return api.get(`/products?${queryParams.toString()}`);\n  },\n  getLowStockProducts: () => api.get('/products/low-stock'),\n  getProduct: (id: number) => api.get(`/products/${id}`),\n  createProduct: (productData: any) => api.post('/products', productData),\n  updateProduct: (id: number, productData: any) => api.put(`/products/${id}`, productData),\n  deleteProduct: (id: number) => api.delete(`/products/${id}`),\n};\n\n// خدمات المخزون\nexport const inventoryAPI = {\n  getMovements: (params: any = {}) => {\n    const queryParams = new URLSearchParams();\n    Object.keys(params).forEach(key => {\n      if (params[key] !== undefined && params[key] !== null) {\n        queryParams.append(key, params[key]);\n      }\n    });\n    return api.get(`/inventory/movements?${queryParams.toString()}`);\n  },\n  createMovement: (movementData: any) => api.post('/inventory/movements', movementData),\n  adjustStock: (adjustmentData: any) => api.post('/inventory/adjust-stock', adjustmentData),\n  getStockLevels: (lowStockOnly = false) => \n    api.get(`/inventory/stock-levels?low_stock_only=${lowStockOnly}`),\n};\n\n// خدمات التقارير\nexport const reportsAPI = {\n  getDashboardStats: () => api.get('/reports/dashboard'),\n  getInventorySummary: (categoryId?: number) => {\n    let url = '/reports/inventory-summary';\n    if (categoryId) url += `?category_id=${categoryId}`;\n    return api.get(url);\n  },\n  getMovementAnalysis: (startDate?: string, endDate?: string) => {\n    let url = '/reports/movement-analysis';\n    const params = new URLSearchParams();\n    if (startDate) params.append('start_date', startDate);\n    if (endDate) params.append('end_date', endDate);\n    if (params.toString()) url += `?${params.toString()}`;\n    return api.get(url);\n  },\n  getLowStockAlert: () => api.get('/reports/low-stock-alert'),\n  getSupplierAnalysis: () => api.get('/reports/supplier-analysis'),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClD,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;IACvCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,OAAO,GAAG;EACrBC,KAAK,EAAEA,CAACC,QAAgB,EAAEC,QAAgB,KAAK;IAC7C;IACA,OAAOzB,GAAG,CAAC0B,IAAI,CAAC,aAAa,EAAE;MAC7BF,QAAQ;MACRC;IACF,CAAC,CAAC;EACJ,CAAC;EACDE,QAAQ,EAAGC,QAAa,IAAK5B,GAAG,CAAC0B,IAAI,CAAC,gBAAgB,EAAEE,QAAQ,CAAC;EACjEC,cAAc,EAAEA,CAAA,KAAM7B,GAAG,CAAC8B,GAAG,CAAC,UAAU;AAC1C,CAAC;;AAED;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBC,QAAQ,EAAEA,CAACC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,GAAG,KAAKlC,GAAG,CAAC8B,GAAG,CAAC,eAAeG,IAAI,UAAUC,KAAK,EAAE,CAAC;EAClFC,OAAO,EAAGC,EAAU,IAAKpC,GAAG,CAAC8B,GAAG,CAAC,UAAUM,EAAE,EAAE,CAAC;EAChDC,UAAU,EAAGT,QAAa,IAAK5B,GAAG,CAAC0B,IAAI,CAAC,QAAQ,EAAEE,QAAQ,CAAC;EAC3DU,UAAU,EAAEA,CAACF,EAAU,EAAER,QAAa,KAAK5B,GAAG,CAACuC,GAAG,CAAC,UAAUH,EAAE,EAAE,EAAER,QAAQ,CAAC;EAC5EY,UAAU,EAAGJ,EAAU,IAAKpC,GAAG,CAACyC,MAAM,CAAC,UAAUL,EAAE,EAAE;AACvD,CAAC;;AAED;AACA,OAAO,MAAMM,aAAa,GAAG;EAC3BC,aAAa,EAAEA,CAACV,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,GAAG,KAAKlC,GAAG,CAAC8B,GAAG,CAAC,oBAAoBG,IAAI,UAAUC,KAAK,EAAE,CAAC;EAC5FU,iBAAiB,EAAEA,CAAA,KAAM5C,GAAG,CAAC8B,GAAG,CAAC,kBAAkB,CAAC;EACpDe,WAAW,EAAGT,EAAU,IAAKpC,GAAG,CAAC8B,GAAG,CAAC,eAAeM,EAAE,EAAE,CAAC;EACzDU,cAAc,EAAGC,YAAiB,IAAK/C,GAAG,CAAC0B,IAAI,CAAC,aAAa,EAAEqB,YAAY,CAAC;EAC5EC,cAAc,EAAEA,CAACZ,EAAU,EAAEW,YAAiB,KAAK/C,GAAG,CAACuC,GAAG,CAAC,eAAeH,EAAE,EAAE,EAAEW,YAAY,CAAC;EAC7FE,cAAc,EAAGb,EAAU,IAAKpC,GAAG,CAACyC,MAAM,CAAC,eAAeL,EAAE,EAAE;AAChE,CAAC;;AAED;AACA,OAAO,MAAMc,YAAY,GAAG;EAC1BC,YAAY,EAAEA,CAAClB,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,GAAG,EAAEkB,MAAe,KAAK;IACxD,IAAIC,GAAG,GAAG,mBAAmBpB,IAAI,UAAUC,KAAK,EAAE;IAClD,IAAIkB,MAAM,EAAEC,GAAG,IAAI,WAAWC,kBAAkB,CAACF,MAAM,CAAC,EAAE;IAC1D,OAAOpD,GAAG,CAAC8B,GAAG,CAACuB,GAAG,CAAC;EACrB,CAAC;EACDE,WAAW,EAAGnB,EAAU,IAAKpC,GAAG,CAAC8B,GAAG,CAAC,cAAcM,EAAE,EAAE,CAAC;EACxDoB,cAAc,EAAGC,YAAiB,IAAKzD,GAAG,CAAC0B,IAAI,CAAC,YAAY,EAAE+B,YAAY,CAAC;EAC3EC,cAAc,EAAEA,CAACtB,EAAU,EAAEqB,YAAiB,KAAKzD,GAAG,CAACuC,GAAG,CAAC,cAAcH,EAAE,EAAE,EAAEqB,YAAY,CAAC;EAC5FE,cAAc,EAAGvB,EAAU,IAAKpC,GAAG,CAACyC,MAAM,CAAC,cAAcL,EAAE,EAAE;AAC/D,CAAC;;AAED;AACA,OAAO,MAAMwB,WAAW,GAAG;EACzBC,WAAW,EAAEA,CAACC,MAAW,GAAG,CAAC,CAAC,KAAK;IACjC,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;IACzCC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;MACjC,IAAIN,MAAM,CAACM,GAAG,CAAC,KAAKC,SAAS,IAAIP,MAAM,CAACM,GAAG,CAAC,KAAK,IAAI,EAAE;QACrDL,WAAW,CAACO,MAAM,CAACF,GAAG,EAAEN,MAAM,CAACM,GAAG,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IACF,OAAOpE,GAAG,CAAC8B,GAAG,CAAC,aAAaiC,WAAW,CAACQ,QAAQ,CAAC,CAAC,EAAE,CAAC;EACvD,CAAC;EACDC,mBAAmB,EAAEA,CAAA,KAAMxE,GAAG,CAAC8B,GAAG,CAAC,qBAAqB,CAAC;EACzD2C,UAAU,EAAGrC,EAAU,IAAKpC,GAAG,CAAC8B,GAAG,CAAC,aAAaM,EAAE,EAAE,CAAC;EACtDsC,aAAa,EAAGC,WAAgB,IAAK3E,GAAG,CAAC0B,IAAI,CAAC,WAAW,EAAEiD,WAAW,CAAC;EACvEC,aAAa,EAAEA,CAACxC,EAAU,EAAEuC,WAAgB,KAAK3E,GAAG,CAACuC,GAAG,CAAC,aAAaH,EAAE,EAAE,EAAEuC,WAAW,CAAC;EACxFE,aAAa,EAAGzC,EAAU,IAAKpC,GAAG,CAACyC,MAAM,CAAC,aAAaL,EAAE,EAAE;AAC7D,CAAC;;AAED;AACA,OAAO,MAAM0C,YAAY,GAAG;EAC1BC,YAAY,EAAEA,CAACjB,MAAW,GAAG,CAAC,CAAC,KAAK;IAClC,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;IACzCC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;MACjC,IAAIN,MAAM,CAACM,GAAG,CAAC,KAAKC,SAAS,IAAIP,MAAM,CAACM,GAAG,CAAC,KAAK,IAAI,EAAE;QACrDL,WAAW,CAACO,MAAM,CAACF,GAAG,EAAEN,MAAM,CAACM,GAAG,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IACF,OAAOpE,GAAG,CAAC8B,GAAG,CAAC,wBAAwBiC,WAAW,CAACQ,QAAQ,CAAC,CAAC,EAAE,CAAC;EAClE,CAAC;EACDS,cAAc,EAAGC,YAAiB,IAAKjF,GAAG,CAAC0B,IAAI,CAAC,sBAAsB,EAAEuD,YAAY,CAAC;EACrFC,WAAW,EAAGC,cAAmB,IAAKnF,GAAG,CAAC0B,IAAI,CAAC,yBAAyB,EAAEyD,cAAc,CAAC;EACzFC,cAAc,EAAEA,CAACC,YAAY,GAAG,KAAK,KACnCrF,GAAG,CAAC8B,GAAG,CAAC,0CAA0CuD,YAAY,EAAE;AACpE,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,iBAAiB,EAAEA,CAAA,KAAMvF,GAAG,CAAC8B,GAAG,CAAC,oBAAoB,CAAC;EACtD0D,mBAAmB,EAAGC,UAAmB,IAAK;IAC5C,IAAIpC,GAAG,GAAG,4BAA4B;IACtC,IAAIoC,UAAU,EAAEpC,GAAG,IAAI,gBAAgBoC,UAAU,EAAE;IACnD,OAAOzF,GAAG,CAAC8B,GAAG,CAACuB,GAAG,CAAC;EACrB,CAAC;EACDqC,mBAAmB,EAAEA,CAACC,SAAkB,EAAEC,OAAgB,KAAK;IAC7D,IAAIvC,GAAG,GAAG,4BAA4B;IACtC,MAAMS,MAAM,GAAG,IAAIE,eAAe,CAAC,CAAC;IACpC,IAAI2B,SAAS,EAAE7B,MAAM,CAACQ,MAAM,CAAC,YAAY,EAAEqB,SAAS,CAAC;IACrD,IAAIC,OAAO,EAAE9B,MAAM,CAACQ,MAAM,CAAC,UAAU,EAAEsB,OAAO,CAAC;IAC/C,IAAI9B,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAElB,GAAG,IAAI,IAAIS,MAAM,CAACS,QAAQ,CAAC,CAAC,EAAE;IACrD,OAAOvE,GAAG,CAAC8B,GAAG,CAACuB,GAAG,CAAC;EACrB,CAAC;EACDwC,gBAAgB,EAAEA,CAAA,KAAM7F,GAAG,CAAC8B,GAAG,CAAC,0BAA0B,CAAC;EAC3DgE,mBAAmB,EAAEA,CAAA,KAAM9F,GAAG,CAAC8B,GAAG,CAAC,4BAA4B;AACjE,CAAC;AAED,eAAe9B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}