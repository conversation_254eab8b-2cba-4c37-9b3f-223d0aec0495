{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nlet globalId = 0;\n\n// TODO React 17: Remove `useGlobalId` once React 17 support is removed\nfunction useGlobalId(idOverride) {\n  const [defaultId, setDefaultId] = React.useState(idOverride);\n  const id = idOverride || defaultId;\n  React.useEffect(() => {\n    if (defaultId == null) {\n      // Fallback to this default id when possible.\n      // Use the incrementing value for client-side rendering only.\n      // We can't use it server-side.\n      // If you want to use random values please consider the Birthday Problem: https://en.wikipedia.org/wiki/Birthday_problem\n      globalId += 1;\n      setDefaultId(`mui-${globalId}`);\n    }\n  }, [defaultId]);\n  return id;\n}\n\n// See https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379 for why\nconst safeReact = {\n  ...React\n};\nconst maybeReactUseId = safeReact.useId;\n\n/**\n *\n * @example <div id={useId()} />\n * @param idOverride\n * @returns {string}\n */\nexport default function useId(idOverride) {\n  // React.useId() is only available from React 17.0.0.\n  if (maybeReactUseId !== undefined) {\n    const reactId = maybeReactUseId();\n    return idOverride ?? reactId;\n  }\n\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n  // eslint-disable-next-line react-hooks/rules-of-hooks -- `React.useId` is invariant at runtime.\n  return useGlobalId(idOverride);\n}", "map": {"version": 3, "names": ["React", "globalId", "useGlobalId", "idOverride", "defaultId", "setDefaultId", "useState", "id", "useEffect", "safeReact", "maybeReactUseId", "useId", "undefined", "reactId"], "sources": ["E:/a1/frontend/node_modules/@mui/utils/esm/useId/useId.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nlet globalId = 0;\n\n// TODO React 17: Remove `useGlobalId` once React 17 support is removed\nfunction useGlobalId(idOverride) {\n  const [defaultId, setDefaultId] = React.useState(idOverride);\n  const id = idOverride || defaultId;\n  React.useEffect(() => {\n    if (defaultId == null) {\n      // Fallback to this default id when possible.\n      // Use the incrementing value for client-side rendering only.\n      // We can't use it server-side.\n      // If you want to use random values please consider the Birthday Problem: https://en.wikipedia.org/wiki/Birthday_problem\n      globalId += 1;\n      setDefaultId(`mui-${globalId}`);\n    }\n  }, [defaultId]);\n  return id;\n}\n\n// See https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379 for why\nconst safeReact = {\n  ...React\n};\nconst maybeReactUseId = safeReact.useId;\n\n/**\n *\n * @example <div id={useId()} />\n * @param idOverride\n * @returns {string}\n */\nexport default function useId(idOverride) {\n  // React.useId() is only available from React 17.0.0.\n  if (maybeReactUseId !== undefined) {\n    const reactId = maybeReactUseId();\n    return idOverride ?? reactId;\n  }\n\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n  // eslint-disable-next-line react-hooks/rules-of-hooks -- `React.useId` is invariant at runtime.\n  return useGlobalId(idOverride);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,QAAQ,GAAG,CAAC;;AAEhB;AACA,SAASC,WAAWA,CAACC,UAAU,EAAE;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGL,KAAK,CAACM,QAAQ,CAACH,UAAU,CAAC;EAC5D,MAAMI,EAAE,GAAGJ,UAAU,IAAIC,SAAS;EAClCJ,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,IAAIJ,SAAS,IAAI,IAAI,EAAE;MACrB;MACA;MACA;MACA;MACAH,QAAQ,IAAI,CAAC;MACbI,YAAY,CAAC,OAAOJ,QAAQ,EAAE,CAAC;IACjC;EACF,CAAC,EAAE,CAACG,SAAS,CAAC,CAAC;EACf,OAAOG,EAAE;AACX;;AAEA;AACA,MAAME,SAAS,GAAG;EAChB,GAAGT;AACL,CAAC;AACD,MAAMU,eAAe,GAAGD,SAAS,CAACE,KAAK;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,KAAKA,CAACR,UAAU,EAAE;EACxC;EACA,IAAIO,eAAe,KAAKE,SAAS,EAAE;IACjC,MAAMC,OAAO,GAAGH,eAAe,CAAC,CAAC;IACjC,OAAOP,UAAU,IAAIU,OAAO;EAC9B;;EAEA;EACA;EACA,OAAOX,WAAW,CAACC,UAAU,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}