#!/usr/bin/env python3
"""
تشغيل مبسط للنظام
"""
import subprocess
import sys
import os

def main():
    print("🚀 تشغيل نظام إدارة المخزون - إصدار مبسط")
    print("=" * 50)
    
    print("🔧 إصلاح المشاكل...")
    try:
        subprocess.run([sys.executable, "fix_login.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ فشل في إصلاح المشاكل")
        return
    
    print("\n🚀 تشغيل الخادم...")
    try:
        subprocess.run([sys.executable, "test_server.py"], check=True)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except subprocess.CalledProcessError:
        print("❌ فشل في تشغيل الخادم")

if __name__ == "__main__":
    main()
