#!/usr/bin/env python3
"""
خادم مبسط جداً بدون مكتبات معقدة
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import hashlib
import json
import os

app = FastAPI(title="نظام إدارة المخزون - اختبار مبسط")

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# نماذج البيانات
class LoginRequest(BaseModel):
    username: str
    password: str

# ملف بيانات المستخدمين
USERS_FILE = "users.json"

def hash_password(password: str) -> str:
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """التحقق من كلمة المرور"""
    return hash_password(plain_password) == hashed_password

def create_default_user():
    """إنشاء مستخدم افتراضي"""
    users = {
        "admin": {
            "username": "admin",
            "email": "<EMAIL>",
            "full_name": "مدير النظام",
            "hashed_password": hash_password("admin123"),
            "role": "admin",
            "is_active": True
        }
    }
    
    with open(USERS_FILE, "w", encoding="utf-8") as f:
        json.dump(users, f, ensure_ascii=False, indent=2)
    
    print("✅ تم إنشاء المستخدم الافتراضي")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")

def load_users():
    """تحميل المستخدمين"""
    if not os.path.exists(USERS_FILE):
        create_default_user()
    
    with open(USERS_FILE, "r", encoding="utf-8") as f:
        return json.load(f)

# إنشاء المستخدم الافتراضي عند بدء التطبيق
print("🚀 بدء تشغيل الخادم المبسط...")
create_default_user()

@app.get("/")
async def root():
    return {"message": "مرحباً بك في نظام إدارة المخزون - اختبار مبسط"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.post("/api/auth/login")
async def login_simple(login_data: LoginRequest):
    """تسجيل الدخول المبسط"""
    try:
        users = load_users()
        
        # البحث عن المستخدم
        if login_data.username not in users:
            raise HTTPException(
                status_code=401,
                detail="اسم المستخدم غير صحيح"
            )
        
        user = users[login_data.username]
        
        # التحقق من كلمة المرور
        if not verify_password(login_data.password, user["hashed_password"]):
            raise HTTPException(
                status_code=401,
                detail="كلمة المرور غير صحيحة"
            )
        
        if not user["is_active"]:
            raise HTTPException(
                status_code=400,
                detail="المستخدم غير نشط"
            )
        
        # إنشاء توكن مبسط
        token = f"simple_token_{user['username']}_12345"
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "user": {
                "username": user["username"],
                "full_name": user["full_name"],
                "email": user["email"],
                "role": user["role"]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في الخادم: {str(e)}"
        )

# دعم OAuth2PasswordRequestForm للتوافق مع Frontend
@app.post("/api/auth/login-form")
async def login_form(username: str, password: str):
    """تسجيل الدخول بـ form data"""
    login_data = LoginRequest(username=username, password=password)
    return await login_simple(login_data)

@app.get("/api/auth/me")
async def read_users_me():
    """معلومات المستخدم الحالي"""
    return {
        "username": "admin",
        "full_name": "مدير النظام",
        "email": "<EMAIL>",
        "role": "admin"
    }

# بيانات تجريبية للمنتجات
@app.get("/api/products")
async def get_products():
    """الحصول على المنتجات"""
    return [
        {
            "id": 1,
            "code": "DESK001",
            "name": "مكتب مكتبي",
            "category_name": "أثاث",
            "current_stock": 3,
            "min_stock": 5,
            "unit": "قطعة",
            "is_low_stock": True,
            "selling_price": 2000
        },
        {
            "id": 2,
            "code": "CHAIR001", 
            "name": "كرسي مكتبي",
            "category_name": "أثاث",
            "current_stock": 2,
            "min_stock": 5,
            "unit": "قطعة",
            "is_low_stock": True,
            "selling_price": 1200
        }
    ]

@app.get("/api/reports/dashboard")
async def get_dashboard_stats():
    """إحصائيات لوحة التحكم"""
    return {
        "total_products": 2,
        "total_categories": 3,
        "total_suppliers": 2,
        "low_stock_products": 2,
        "total_inventory_value": 6400.0,
        "today_movements": 0,
        "week_movements": 0
    }

@app.get("/api/reports/low-stock-alert")
async def get_low_stock_alert():
    """تنبيهات المخزون المنخفض"""
    return {
        "low_stock": [
            {
                "id": 1,
                "code": "DESK001",
                "name": "مكتب مكتبي",
                "category": "أثاث",
                "current_stock": 3,
                "min_stock": 5
            },
            {
                "id": 2,
                "code": "CHAIR001",
                "name": "كرسي مكتبي", 
                "category": "أثاث",
                "current_stock": 2,
                "min_stock": 5
            }
        ],
        "out_of_stock": [],
        "summary": {
            "low_stock_count": 2,
            "out_of_stock_count": 0
        }
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 تشغيل الخادم المبسط...")
    print("📍 الرابط: http://localhost:8000")
    print("📚 التوثيق: http://localhost:8000/docs")
    print("👤 بيانات الدخول: admin / admin123")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
