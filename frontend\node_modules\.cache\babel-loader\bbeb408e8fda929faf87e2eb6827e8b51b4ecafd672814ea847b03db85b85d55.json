{"ast": null, "code": "import * as React from 'react';\nexport default function isMuiElement(element, muiNames) {\n  return /*#__PURE__*/React.isValidElement(element) && muiNames.indexOf(\n  // For server components `muiName` is avaialble in element.type._payload.value.muiName\n  // relevant info - https://github.com/facebook/react/blob/2807d781a08db8e9873687fccc25c0f12b4fb3d4/packages/react/src/ReactLazy.js#L45\n  // eslint-disable-next-line no-underscore-dangle\n  element.type.muiName ?? element.type?._payload?.value?.muiName) !== -1;\n}", "map": {"version": 3, "names": ["React", "isMuiElement", "element", "muiNames", "isValidElement", "indexOf", "type", "mui<PERSON><PERSON>", "_payload", "value"], "sources": ["E:/a1/frontend/node_modules/@mui/utils/esm/isMuiElement/isMuiElement.js"], "sourcesContent": ["import * as React from 'react';\nexport default function isMuiElement(element, muiNames) {\n  return /*#__PURE__*/React.isValidElement(element) && muiNames.indexOf(\n  // For server components `muiName` is avaialble in element.type._payload.value.muiName\n  // relevant info - https://github.com/facebook/react/blob/2807d781a08db8e9873687fccc25c0f12b4fb3d4/packages/react/src/ReactLazy.js#L45\n  // eslint-disable-next-line no-underscore-dangle\n  element.type.muiName ?? element.type?._payload?.value?.muiName) !== -1;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,YAAYA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACtD,OAAO,aAAaH,KAAK,CAACI,cAAc,CAACF,OAAO,CAAC,IAAIC,QAAQ,CAACE,OAAO;EACrE;EACA;EACA;EACAH,OAAO,CAACI,IAAI,CAACC,OAAO,IAAIL,OAAO,CAACI,IAAI,EAAEE,QAAQ,EAAEC,KAAK,EAAEF,OAAO,CAAC,KAAK,CAAC,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}