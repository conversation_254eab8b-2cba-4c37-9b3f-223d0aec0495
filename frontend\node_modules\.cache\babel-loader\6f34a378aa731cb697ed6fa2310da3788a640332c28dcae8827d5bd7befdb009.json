{"ast": null, "code": "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "map": {"version": 3, "names": ["max", "mathMax", "min", "mathMin", "within", "value", "withinMaxClamp", "v"], "sources": ["E:/a1/frontend/node_modules/@popperjs/core/lib/utils/within.js"], "sourcesContent": ["import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}"], "mappings": "AAAA,SAASA,GAAG,IAAIC,OAAO,EAAEC,GAAG,IAAIC,OAAO,QAAQ,WAAW;AAC1D,OAAO,SAASC,MAAMA,CAACF,GAAG,EAAEG,KAAK,EAAEL,GAAG,EAAE;EACtC,OAAOC,OAAO,CAACC,GAAG,EAAEC,OAAO,CAACE,KAAK,EAAEL,GAAG,CAAC,CAAC;AAC1C;AACA,OAAO,SAASM,cAAcA,CAACJ,GAAG,EAAEG,KAAK,EAAEL,GAAG,EAAE;EAC9C,IAAIO,CAAC,GAAGH,MAAM,CAACF,GAAG,EAAEG,KAAK,EAAEL,GAAG,CAAC;EAC/B,OAAOO,CAAC,GAAGP,GAAG,GAAGA,GAAG,GAAGO,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}