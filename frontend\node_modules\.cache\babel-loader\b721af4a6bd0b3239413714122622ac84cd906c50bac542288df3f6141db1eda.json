{"ast": null, "code": "import * as React from 'react';\n\n/**\n * Returns the ref of a React element handling differences between React 19 and older versions.\n * It will throw runtime error if the element is not a valid React element.\n *\n * @param element React.ReactElement\n * @returns React.Ref<any> | null\n */\nexport default function getReactElementRef(element) {\n  // 'ref' is passed as prop in React 19, whereas 'ref' is directly attached to children in older versions\n  if (parseInt(React.version, 10) >= 19) {\n    return element?.props?.ref || null;\n  }\n  // @ts-expect-error element.ref is not included in the ReactElement type\n  // https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/70189\n  return element?.ref || null;\n}", "map": {"version": 3, "names": ["React", "getReactElementRef", "element", "parseInt", "version", "props", "ref"], "sources": ["E:/a1/frontend/node_modules/@mui/utils/esm/getReactElementRef/getReactElementRef.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Returns the ref of a React element handling differences between React 19 and older versions.\n * It will throw runtime error if the element is not a valid React element.\n *\n * @param element React.ReactElement\n * @returns React.Ref<any> | null\n */\nexport default function getReactElementRef(element) {\n  // 'ref' is passed as prop in React 19, whereas 'ref' is directly attached to children in older versions\n  if (parseInt(React.version, 10) >= 19) {\n    return element?.props?.ref || null;\n  }\n  // @ts-expect-error element.ref is not included in the ReactElement type\n  // https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/70189\n  return element?.ref || null;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,OAAO,EAAE;EAClD;EACA,IAAIC,QAAQ,CAACH,KAAK,CAACI,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE;IACrC,OAAOF,OAAO,EAAEG,KAAK,EAAEC,GAAG,IAAI,IAAI;EACpC;EACA;EACA;EACA,OAAOJ,OAAO,EAAEI,GAAG,IAAI,IAAI;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}