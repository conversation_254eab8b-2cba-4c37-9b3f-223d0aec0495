{"ast": null, "code": "export { default } from \"./sizing.js\";\nexport * from \"./sizing.js\";", "map": {"version": 3, "names": ["default"], "sources": ["E:/a1/frontend/node_modules/@mui/system/esm/sizing/index.js"], "sourcesContent": ["export { default } from \"./sizing.js\";\nexport * from \"./sizing.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,cAAc,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}