import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Chip,
  Button
} from '@mui/material';
import {
  Inventory,
  Category,
  LocalShipping,
  Warning
} from '@mui/icons-material';
import { reportsAPI } from '../services/api';

interface DashboardStats {
  total_products: number;
  total_categories: number;
  total_suppliers: number;
  low_stock_products: number;
  total_inventory_value: number;
  today_movements: number;
  week_movements: number;
}

interface LowStockAlert {
  low_stock: Array<{
    id: number;
    code: string;
    name: string;
    category: string;
    current_stock: number;
    min_stock: number;
  }>;
  out_of_stock: Array<{
    id: number;
    code: string;
    name: string;
    category: string;
  }>;
  summary: {
    low_stock_count: number;
    out_of_stock_count: number;
  };
}

const SimpleDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [alerts, setAlerts] = useState<LowStockAlert | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      console.log('جاري تحميل بيانات لوحة التحكم...');
      
      const [statsResponse, alertsResponse] = await Promise.all([
        reportsAPI.getDashboardStats(),
        reportsAPI.getLowStockAlert()
      ]);
      
      console.log('بيانات الإحصائيات:', statsResponse.data);
      console.log('بيانات التنبيهات:', alertsResponse.data);
      
      setStats(statsResponse.data);
      setAlerts(alertsResponse.data);
    } catch (err: any) {
      console.error('خطأ في تحميل البيانات:', err);
      setError('حدث خطأ أثناء تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>جاري تحميل البيانات...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button onClick={fetchDashboardData} variant="contained">
          إعادة المحاولة
        </Button>
      </Box>
    );
  }

  const StatCard = ({ title, value, icon, color = 'primary' }: any) => (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="h6">
              {title}
            </Typography>
            <Typography variant="h4" component="h2">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </Typography>
          </Box>
          <Box color={`${color}.main`}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        لوحة التحكم
      </Typography>

      {/* الإحصائيات الرئيسية */}
      <Box sx={{ mb: 3 }}>
        <StatCard
          title="إجمالي المنتجات"
          value={stats?.total_products}
          icon={<Inventory fontSize="large" />}
          color="primary"
        />
        
        <StatCard
          title="الفئات"
          value={stats?.total_categories}
          icon={<Category fontSize="large" />}
          color="secondary"
        />
        
        <StatCard
          title="الموردين"
          value={stats?.total_suppliers}
          icon={<LocalShipping fontSize="large" />}
          color="info"
        />
        
        <StatCard
          title="منتجات منخفضة المخزون"
          value={stats?.low_stock_products}
          icon={<Warning fontSize="large" />}
          color="warning"
        />
      </Box>

      {/* معلومات المخزون */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            معلومات المخزون
          </Typography>
          <Box sx={{ mt: 2 }}>
            <Box display="flex" justifyContent="space-between" sx={{ mb: 2 }}>
              <Typography variant="body1">
                إجمالي قيمة المخزون
              </Typography>
              <Typography variant="h6" color="primary">
                {stats?.total_inventory_value?.toLocaleString()} ج.م
              </Typography>
            </Box>
            <Box display="flex" justifyContent="space-between" sx={{ mb: 1 }}>
              <Typography variant="body2">
                حركات اليوم
              </Typography>
              <Typography variant="h6">
                {stats?.today_movements}
              </Typography>
            </Box>
            <Box display="flex" justifyContent="space-between">
              <Typography variant="body2">
                حركات الأسبوع
              </Typography>
              <Typography variant="h6">
                {stats?.week_movements}
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* تنبيهات المخزون */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            تنبيهات المخزون
          </Typography>
          
          {alerts?.summary.out_of_stock_count > 0 && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {alerts.summary.out_of_stock_count} منتج نافد المخزون
            </Alert>
          )}
          
          {alerts?.summary.low_stock_count > 0 && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              {alerts.summary.low_stock_count} منتج منخفض المخزون
            </Alert>
          )}

          {alerts && (alerts.low_stock.length > 0 || alerts.out_of_stock.length > 0) ? (
            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
              <List dense>
                {alerts.out_of_stock.slice(0, 5).map((product) => (
                  <ListItem key={product.id}>
                    <ListItemText
                      primary={`${product.code} - ${product.name}`}
                      secondary={product.category}
                    />
                    <Chip label="نافد" color="error" size="small" />
                  </ListItem>
                ))}
                {alerts.low_stock.slice(0, 5).map((product) => (
                  <ListItem key={product.id}>
                    <ListItemText
                      primary={`${product.code} - ${product.name}`}
                      secondary={`المخزون: ${product.current_stock} / الحد الأدنى: ${product.min_stock}`}
                    />
                    <Chip label="منخفض" color="warning" size="small" />
                  </ListItem>
                ))}
              </List>
            </Box>
          ) : (
            <Typography variant="body2" color="textSecondary">
              لا توجد تنبيهات مخزون حالياً
            </Typography>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default SimpleDashboard;
