#!/usr/bin/env python3
"""
اختبار سريع للنظام
"""
import asyncio
import sys
import os

async def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        from tortoise import Tortoise
        
        DATABASE_URL = "postgres://inventory_user:inventory_pass@localhost:5432/inventory_db"
        
        print("🔍 اختبار الاتصال بقاعدة البيانات...")
        
        await Tortoise.init(
            db_url=DATABASE_URL,
            modules={"models": ["app.models"]}
        )
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # إنشاء الجداول
        await Tortoise.generate_schemas()
        print("✅ تم إنشاء الجداول بنجاح")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

async def test_user_creation():
    """اختبار إنشاء مستخدم"""
    try:
        from tortoise import Tortoise
        from app.models.user import User, UserRole
        from app.auth.security import get_password_hash
        
        DATABASE_URL = "postgres://inventory_user:inventory_pass@localhost:5432/inventory_db"
        
        await Tortoise.init(
            db_url=DATABASE_URL,
            modules={"models": ["app.models"]}
        )
        
        print("🔍 اختبار إنشاء مستخدم...")
        
        # حذف المستخدم إذا كان موجوداً
        await User.filter(username="admin").delete()
        
        # إنشاء مستخدم جديد
        admin_user = await User.create(
            username="admin",
            email="<EMAIL>",
            full_name="مدير النظام",
            hashed_password=get_password_hash("admin123"),
            role=UserRole.ADMIN,
            is_active=True
        )
        
        print(f"✅ تم إنشاء المستخدم: {admin_user.username}")
        
        # اختبار تسجيل الدخول
        from app.auth.security import verify_password
        
        user = await User.get(username="admin")
        if verify_password("admin123", user.hashed_password):
            print("✅ اختبار كلمة المرور نجح")
        else:
            print("❌ اختبار كلمة المرور فشل")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return False

async def test_imports():
    """اختبار استيراد الوحدات"""
    try:
        print("🔍 اختبار استيراد الوحدات...")
        
        # اختبار استيراد النماذج
        from app.models.user import User, UserRole
        from app.models.category import Category
        from app.models.product import Product
        from app.models.supplier import Supplier
        print("✅ تم استيراد النماذج بنجاح")
        
        # اختبار استيراد المصادقة
        from app.auth.security import get_password_hash, verify_password, create_access_token
        print("✅ تم استيراد نظام المصادقة بنجاح")
        
        # اختبار استيراد الـ schemas
        from app.schemas.user import UserCreate, UserResponse
        print("✅ تم استيراد الـ schemas بنجاح")
        
        # اختبار استيراد الـ routers
        from app.routers import auth
        print("✅ تم استيراد الـ routers بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        return False

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        print("🔍 التحقق من المتطلبات...")
        
        import fastapi
        print(f"✅ FastAPI: {fastapi.__version__}")
        
        import tortoise
        print(f"✅ Tortoise ORM: {tortoise.__version__}")
        
        import pydantic
        print(f"✅ Pydantic: {pydantic.__version__}")
        
        import jose
        print("✅ Python-JOSE متوفر")
        
        import passlib
        print("✅ Passlib متوفر")
        
        return True
        
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار النظام...")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("❌ يرجى تثبيت المتطلبات أولاً: pip install -r requirements.txt")
        return
    
    # اختبار الاستيراد
    if not await test_imports():
        print("❌ فشل في استيراد الوحدات")
        return
    
    # اختبار قاعدة البيانات
    if not await test_database_connection():
        print("❌ فشل في الاتصال بقاعدة البيانات")
        print("💡 تأكد من تشغيل PostgreSQL: docker-compose up -d")
        return
    
    # اختبار إنشاء المستخدم
    if not await test_user_creation():
        print("❌ فشل في إنشاء المستخدم")
        return
    
    print("\n" + "=" * 50)
    print("✅ جميع الاختبارات نجحت!")
    print("🚀 يمكنك الآن تشغيل الخادم:")
    print("   python test_server.py")
    print("   أو")
    print("   python run.py")

if __name__ == "__main__":
    asyncio.run(main())
