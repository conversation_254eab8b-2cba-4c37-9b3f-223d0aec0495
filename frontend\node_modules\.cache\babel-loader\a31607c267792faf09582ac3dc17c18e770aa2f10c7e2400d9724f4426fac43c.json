{"ast": null, "code": "'use client';\n\nimport useForkRef from \"../useForkRef/index.js\";\nimport appendOwnerState from \"../appendOwnerState/index.js\";\nimport mergeSlotProps from \"../mergeSlotProps/index.js\";\nimport resolveComponentProps from \"../resolveComponentProps/index.js\";\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  const {\n    elementType,\n    externalSlotProps,\n    ownerState,\n    skipResolvingSlotProps = false,\n    ...other\n  } = parameters;\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps({\n    ...other,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.additionalProps?.ref);\n  const props = appendOwnerState(elementType, {\n    ...mergedProps,\n    ref\n  }, ownerState);\n  return props;\n}\nexport default useSlotProps;", "map": {"version": 3, "names": ["useForkRef", "appendOwnerState", "mergeSlotProps", "resolveComponentProps", "useSlotProps", "parameters", "elementType", "externalSlotProps", "ownerState", "skipResolvingSlotProps", "other", "resolvedComponentsProps", "props", "mergedProps", "internalRef", "ref", "additionalProps"], "sources": ["E:/a1/frontend/node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js"], "sourcesContent": ["'use client';\n\nimport useForkRef from \"../useForkRef/index.js\";\nimport appendOwnerState from \"../appendOwnerState/index.js\";\nimport mergeSlotProps from \"../mergeSlotProps/index.js\";\nimport resolveComponentProps from \"../resolveComponentProps/index.js\";\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  const {\n    elementType,\n    externalSlotProps,\n    ownerState,\n    skipResolvingSlotProps = false,\n    ...other\n  } = parameters;\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps({\n    ...other,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.additionalProps?.ref);\n  const props = appendOwnerState(elementType, {\n    ...mergedProps,\n    ref\n  }, ownerState);\n  return props;\n}\nexport default useSlotProps;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,qBAAqB,MAAM,mCAAmC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,UAAU,EAAE;EAChC,MAAM;IACJC,WAAW;IACXC,iBAAiB;IACjBC,UAAU;IACVC,sBAAsB,GAAG,KAAK;IAC9B,GAAGC;EACL,CAAC,GAAGL,UAAU;EACd,MAAMM,uBAAuB,GAAGF,sBAAsB,GAAG,CAAC,CAAC,GAAGN,qBAAqB,CAACI,iBAAiB,EAAEC,UAAU,CAAC;EAClH,MAAM;IACJI,KAAK,EAAEC,WAAW;IAClBC;EACF,CAAC,GAAGZ,cAAc,CAAC;IACjB,GAAGQ,KAAK;IACRH,iBAAiB,EAAEI;EACrB,CAAC,CAAC;EACF,MAAMI,GAAG,GAAGf,UAAU,CAACc,WAAW,EAAEH,uBAAuB,EAAEI,GAAG,EAAEV,UAAU,CAACW,eAAe,EAAED,GAAG,CAAC;EAClG,MAAMH,KAAK,GAAGX,gBAAgB,CAACK,WAAW,EAAE;IAC1C,GAAGO,WAAW;IACdE;EACF,CAAC,EAAEP,UAAU,CAAC;EACd,OAAOI,KAAK;AACd;AACA,eAAeR,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}