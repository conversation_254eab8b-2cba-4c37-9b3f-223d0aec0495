from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from tortoise.contrib.fastapi import register_tortoise
from app.core.config import settings

# Import routers individually to catch any import errors
try:
    from app.routers import auth
    from app.routers import users
    from app.routers import categories
    from app.routers import products
    from app.routers import suppliers
    from app.routers import inventory
    from app.routers import reports
    print("✅ All routers imported successfully")
except ImportError as e:
    print(f"❌ Router import error: {e}")
    # Create minimal routers if import fails
    from fastapi import APIRouter
    auth = APIRouter()
    users = APIRouter()
    categories = APIRouter()
    products = APIRouter()
    suppliers = APIRouter()
    inventory = APIRouter()
    reports = APIRouter()

app = FastAPI(
    title="نظام إدارة المخزون",
    description="Inventory Management System API",
    version="1.0.0"
)

# إعداد CORS للسماح للفرونت إند بالوصول للAPI
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# تسجيل المسارات (Routes)
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(users.router, prefix="/api/users", tags=["Users"])
app.include_router(categories.router, prefix="/api/categories", tags=["Categories"])
app.include_router(products.router, prefix="/api/products", tags=["Products"])
app.include_router(suppliers.router, prefix="/api/suppliers", tags=["Suppliers"])
app.include_router(inventory.router, prefix="/api/inventory", tags=["Inventory"])
app.include_router(reports.router, prefix="/api/reports", tags=["Reports"])

# إعداد قاعدة البيانات
register_tortoise(
    app,
    db_url=settings.DATABASE_URL,
    modules={"models": ["app.models"]},
    generate_schemas=True,
    add_exception_handlers=True,
)

@app.get("/")
async def root():
    return {"message": "مرحباً بك في نظام إدارة المخزون"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
