import React, { useState } from 'react';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress
} from '@mui/material';
import { authAPI } from '../services/api';

interface SimpleLoginProps {
  onLogin: () => void;
}

const SimpleLogin: React.FC<SimpleLoginProps> = ({ onLogin }) => {
  const [username, setUsername] = useState('admin');
  const [password, setPassword] = useState('admin123');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      console.log('محاولة تسجيل الدخول...', { username, password });
      
      const response = await authAPI.login(username, password);
      console.log('استجابة تسجيل الدخول:', response.data);
      
      const { access_token } = response.data;
      
      // حفظ التوكن في localStorage
      localStorage.setItem('access_token', access_token);
      
      console.log('تم حفظ التوكن بنجاح');
      
      // استدعاء callback للتسجيل الناجح
      onLogin();
    } catch (err: any) {
      console.error('خطأ في تسجيل الدخول:', err);
      setError(
        err.response?.data?.detail || 
        err.message ||
        'حدث خطأ أثناء تسجيل الدخول'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>
          <Typography component="h1" variant="h4" align="center" gutterBottom>
            نظام إدارة المخزون
          </Typography>
          <Typography component="h2" variant="h6" align="center" gutterBottom>
            تسجيل الدخول
          </Typography>
          
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="اسم المستخدم"
              name="username"
              autoComplete="username"
              autoFocus
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              disabled={loading}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="كلمة المرور"
              type="password"
              id="password"
              autoComplete="current-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
            
            <Box sx={{ mt: 2, mb: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
              <Typography variant="body2" align="center">
                <strong>بيانات الدخول التجريبية:</strong><br />
                اسم المستخدم: admin<br />
                كلمة المرور: admin123
              </Typography>
            </Box>
            
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? (
                <CircularProgress size={24} />
              ) : (
                'تسجيل الدخول'
              )}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default SimpleLogin;
