import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

// إنشاء instance من axios
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// إضافة interceptor للتوكن
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// إضافة interceptor للاستجابة
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // إزالة التوكن وإعادة توجيه للتسجيل
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// خدمات المصادقة
export const authAPI = {
  login: (username: string, password: string) => {
    // استخدام JSON بدلاً من FormData للخادم المبسط
    return api.post('/auth/login', {
      username,
      password
    });
  },
  register: (userData: any) => api.post('/auth/register', userData),
  getCurrentUser: () => api.get('/auth/me'),
};

// خدمات المستخدمين
export const usersAPI = {
  getUsers: (skip = 0, limit = 100) => api.get(`/users?skip=${skip}&limit=${limit}`),
  getUser: (id: number) => api.get(`/users/${id}`),
  createUser: (userData: any) => api.post('/users', userData),
  updateUser: (id: number, userData: any) => api.put(`/users/${id}`, userData),
  deleteUser: (id: number) => api.delete(`/users/${id}`),
};

// خدمات الفئات
export const categoriesAPI = {
  getCategories: (skip = 0, limit = 100) => api.get(`/categories?skip=${skip}&limit=${limit}`),
  getCategoriesTree: () => api.get('/categories/tree'),
  getCategory: (id: number) => api.get(`/categories/${id}`),
  createCategory: (categoryData: any) => api.post('/categories', categoryData),
  updateCategory: (id: number, categoryData: any) => api.put(`/categories/${id}`, categoryData),
  deleteCategory: (id: number) => api.delete(`/categories/${id}`),
};

// خدمات الموردين
export const suppliersAPI = {
  getSuppliers: (skip = 0, limit = 100, search?: string) => {
    let url = `/suppliers?skip=${skip}&limit=${limit}`;
    if (search) url += `&search=${encodeURIComponent(search)}`;
    return api.get(url);
  },
  getSupplier: (id: number) => api.get(`/suppliers/${id}`),
  createSupplier: (supplierData: any) => api.post('/suppliers', supplierData),
  updateSupplier: (id: number, supplierData: any) => api.put(`/suppliers/${id}`, supplierData),
  deleteSupplier: (id: number) => api.delete(`/suppliers/${id}`),
};

// خدمات المنتجات
export const productsAPI = {
  getProducts: (params: any = {}) => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        queryParams.append(key, params[key]);
      }
    });
    return api.get(`/products?${queryParams.toString()}`);
  },
  getLowStockProducts: () => api.get('/products/low-stock'),
  getProduct: (id: number) => api.get(`/products/${id}`),
  createProduct: (productData: any) => api.post('/products', productData),
  updateProduct: (id: number, productData: any) => api.put(`/products/${id}`, productData),
  deleteProduct: (id: number) => api.delete(`/products/${id}`),
};

// خدمات المخزون
export const inventoryAPI = {
  getMovements: (params: any = {}) => {
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        queryParams.append(key, params[key]);
      }
    });
    return api.get(`/inventory/movements?${queryParams.toString()}`);
  },
  createMovement: (movementData: any) => api.post('/inventory/movements', movementData),
  adjustStock: (adjustmentData: any) => api.post('/inventory/adjust-stock', adjustmentData),
  getStockLevels: (lowStockOnly = false) => 
    api.get(`/inventory/stock-levels?low_stock_only=${lowStockOnly}`),
};

// خدمات التقارير
export const reportsAPI = {
  getDashboardStats: () => api.get('/reports/dashboard'),
  getInventorySummary: (categoryId?: number) => {
    let url = '/reports/inventory-summary';
    if (categoryId) url += `?category_id=${categoryId}`;
    return api.get(url);
  },
  getMovementAnalysis: (startDate?: string, endDate?: string) => {
    let url = '/reports/movement-analysis';
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (params.toString()) url += `?${params.toString()}`;
    return api.get(url);
  },
  getLowStockAlert: () => api.get('/reports/low-stock-alert'),
  getSupplierAnalysis: () => api.get('/reports/supplier-analysis'),
};

export default api;
