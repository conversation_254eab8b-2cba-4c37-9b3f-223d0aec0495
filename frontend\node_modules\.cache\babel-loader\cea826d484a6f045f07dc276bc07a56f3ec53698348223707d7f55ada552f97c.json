{"ast": null, "code": "import deepmerge from '@mui/utils/deepmerge';\nimport createBreakpoints from \"../createBreakpoints/createBreakpoints.js\";\nimport cssContainerQueries from \"../cssContainerQueries/index.js\";\nimport shape from \"./shape.js\";\nimport createSpacing from \"./createSpacing.js\";\nimport styleFunctionSx from \"../styleFunctionSx/styleFunctionSx.js\";\nimport defaultSxConfig from \"../styleFunctionSx/defaultSxConfig.js\";\nimport applyStyles from \"./applyStyles.js\";\nfunction createTheme(options = {}, ...args) {\n  const {\n    breakpoints: breakpointsInput = {},\n    palette: paletteInput = {},\n    spacing: spacingInput,\n    shape: shapeInput = {},\n    ...other\n  } = options;\n  const breakpoints = createBreakpoints(breakpointsInput);\n  const spacing = createSpacing(spacingInput);\n  let muiTheme = deepmerge({\n    breakpoints,\n    direction: 'ltr',\n    components: {},\n    // Inject component definitions.\n    palette: {\n      mode: 'light',\n      ...paletteInput\n    },\n    spacing,\n    shape: {\n      ...shape,\n      ...shapeInput\n    }\n  }, other);\n  muiTheme = cssContainerQueries(muiTheme);\n  muiTheme.applyStyles = applyStyles;\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nexport default createTheme;", "map": {"version": 3, "names": ["deepmerge", "createBreakpoints", "cssContainerQueries", "shape", "createSpacing", "styleFunctionSx", "defaultSxConfig", "applyStyles", "createTheme", "options", "args", "breakpoints", "breakpointsInput", "palette", "paletteInput", "spacing", "spacingInput", "shapeInput", "other", "muiTheme", "direction", "components", "mode", "reduce", "acc", "argument", "unstable_sxConfig", "unstable_sx", "sx", "props", "theme"], "sources": ["E:/a1/frontend/node_modules/@mui/system/esm/createTheme/createTheme.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nimport createBreakpoints from \"../createBreakpoints/createBreakpoints.js\";\nimport cssContainerQueries from \"../cssContainerQueries/index.js\";\nimport shape from \"./shape.js\";\nimport createSpacing from \"./createSpacing.js\";\nimport styleFunctionSx from \"../styleFunctionSx/styleFunctionSx.js\";\nimport defaultSxConfig from \"../styleFunctionSx/defaultSxConfig.js\";\nimport applyStyles from \"./applyStyles.js\";\nfunction createTheme(options = {}, ...args) {\n  const {\n    breakpoints: breakpointsInput = {},\n    palette: paletteInput = {},\n    spacing: spacingInput,\n    shape: shapeInput = {},\n    ...other\n  } = options;\n  const breakpoints = createBreakpoints(breakpointsInput);\n  const spacing = createSpacing(spacingInput);\n  let muiTheme = deepmerge({\n    breakpoints,\n    direction: 'ltr',\n    components: {},\n    // Inject component definitions.\n    palette: {\n      mode: 'light',\n      ...paletteInput\n    },\n    spacing,\n    shape: {\n      ...shape,\n      ...shapeInput\n    }\n  }, other);\n  muiTheme = cssContainerQueries(muiTheme);\n  muiTheme.applyStyles = applyStyles;\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nexport default createTheme;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,iBAAiB,MAAM,2CAA2C;AACzE,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE,GAAGC,IAAI,EAAE;EAC1C,MAAM;IACJC,WAAW,EAAEC,gBAAgB,GAAG,CAAC,CAAC;IAClCC,OAAO,EAAEC,YAAY,GAAG,CAAC,CAAC;IAC1BC,OAAO,EAAEC,YAAY;IACrBb,KAAK,EAAEc,UAAU,GAAG,CAAC,CAAC;IACtB,GAAGC;EACL,CAAC,GAAGT,OAAO;EACX,MAAME,WAAW,GAAGV,iBAAiB,CAACW,gBAAgB,CAAC;EACvD,MAAMG,OAAO,GAAGX,aAAa,CAACY,YAAY,CAAC;EAC3C,IAAIG,QAAQ,GAAGnB,SAAS,CAAC;IACvBW,WAAW;IACXS,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE,CAAC,CAAC;IACd;IACAR,OAAO,EAAE;MACPS,IAAI,EAAE,OAAO;MACb,GAAGR;IACL,CAAC;IACDC,OAAO;IACPZ,KAAK,EAAE;MACL,GAAGA,KAAK;MACR,GAAGc;IACL;EACF,CAAC,EAAEC,KAAK,CAAC;EACTC,QAAQ,GAAGjB,mBAAmB,CAACiB,QAAQ,CAAC;EACxCA,QAAQ,CAACZ,WAAW,GAAGA,WAAW;EAClCY,QAAQ,GAAGT,IAAI,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKzB,SAAS,CAACwB,GAAG,EAAEC,QAAQ,CAAC,EAAEN,QAAQ,CAAC;EAC7EA,QAAQ,CAACO,iBAAiB,GAAG;IAC3B,GAAGpB,eAAe;IAClB,GAAGY,KAAK,EAAEQ;EACZ,CAAC;EACDP,QAAQ,CAACQ,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;IACxC,OAAOxB,eAAe,CAAC;MACrBuB,EAAE,EAAEC,KAAK;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACD,OAAOX,QAAQ;AACjB;AACA,eAAeX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}