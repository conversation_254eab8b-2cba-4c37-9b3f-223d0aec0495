#!/usr/bin/env python3
"""
ملف اختبار سريع للخادم
"""
import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordRequestForm
from fastapi import Depends
from tortoise import Tortoise
from datetime import timedelta

# إنشاء تطبيق FastAPI بسيط
app = FastAPI(
    title="نظام إدارة المخزون - اختبار",
    description="Inventory Management System API - Test",
    version="1.0.0"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# متغيرات الإعدادات - استخدام SQLite للاختبار
DATABASE_URL = "sqlite://test_inventory.db"
SECRET_KEY = "your-super-secret-key-change-this-in-production-12345"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

@app.on_event("startup")
async def startup_event():
    """تهيئة قاعدة البيانات عند بدء التطبيق"""
    try:
        await Tortoise.init(
            db_url=DATABASE_URL,
            modules={"models": ["app.models"]}
        )
        await Tortoise.generate_schemas()
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # إنشاء مستخدم تجريبي
        await create_test_user()
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """إغلاق الاتصال بقاعدة البيانات"""
    await Tortoise.close_connections()

async def create_test_user():
    """إنشاء مستخدم تجريبي"""
    try:
        from app.models.user import User, UserRole
        from app.auth.security import get_password_hash
        
        # التحقق من وجود المستخدم
        admin_exists = await User.filter(username="admin").exists()
        
        if not admin_exists:
            admin_user = await User.create(
                username="admin",
                email="<EMAIL>",
                full_name="مدير النظام",
                hashed_password=get_password_hash("admin123"),
                role=UserRole.ADMIN,
                is_active=True
            )
            print(f"✅ تم إنشاء مستخدم تجريبي: {admin_user.username}")
        else:
            print("✅ المستخدم التجريبي موجود بالفعل")
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم التجريبي: {e}")

@app.get("/")
async def root():
    return {"message": "مرحباً بك في نظام إدارة المخزون - اختبار"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "database": "connected"}

# نظام مصادقة مبسط
@app.post("/api/auth/login")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """تسجيل الدخول"""
    try:
        from app.models.user import User
        from app.auth.security import verify_password, create_access_token
        
        # البحث عن المستخدم
        user = await User.get_or_none(username=form_data.username)
        
        if not user:
            raise HTTPException(
                status_code=401,
                detail="اسم المستخدم غير صحيح"
            )
        
        if not verify_password(form_data.password, user.hashed_password):
            raise HTTPException(
                status_code=401,
                detail="كلمة المرور غير صحيحة"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=400,
                detail="المستخدم غير نشط"
            )
        
        # إنشاء التوكن
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username}, 
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token, 
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "role": user.role
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"خطأ في الخادم: {str(e)}"
        )

@app.get("/api/auth/me")
async def read_users_me():
    """معلومات المستخدم الحالي"""
    return {
        "id": 1,
        "username": "admin",
        "full_name": "مدير النظام",
        "email": "<EMAIL>",
        "role": "admin"
    }

# تشغيل الخادم
if __name__ == "__main__":
    print("🚀 بدء تشغيل خادم الاختبار...")
    print("📍 الرابط: http://localhost:8000")
    print("📚 التوثيق: http://localhost:8000/docs")
    print("👤 بيانات الدخول: admin / admin123")
    
    uvicorn.run(
        "test_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
